/**
 * @file ACCESSIBILITY MANAGER MODULE
 *
 * @version 1.0.0
 * <AUTHOR>
 * @contributors
 * @license MIT
 *
 * @description
 * Accessibility manager module for The Great Calculator.
 * Calculator accessibility features including screen reader support,
 * keyboard navigation, focus management, and accessibility preference detection.
 * Provides comprehensive WCAG 2.1 AA compliance features.
 *
 * Features:
 * - Screen reader announcements with ARIA live regions
 * - Comprehensive keyboard navigation and shortcuts
 * - Focus management and restoration
 * - Accessibility preference detection (reduced motion, high contrast)
 * - Calculator-specific keyboard shortcuts
 * - Modal and panel focus trapping
 * - Dynamic ARIA attribute management
 * - Screen reader detection heuristics
 *
 * @requires DOM API, MutationObserver, matchMedia
 */

// ------------ TYPE AND JSDOC DEFINITIONS

/**
 * @typedef {Object} KeyboardNavigation
 * @property {HTMLElement|null} currentFocus - Currently focused element
 * @property {HTMLElement[]} focusableElements - Array of focusable elements
 * @property {Function} updateFocusableElements - Updates focusable elements list
 * @property {Function} handleKeyDown - Handles keyboard events
 */

/**
 * @typedef {Object} FocusHistoryItem
 * @property {HTMLElement} element - The focused element
 * @property {number} timestamp - When the focus occurred
 * @property {boolean} [stored] - Whether this was explicitly stored for restoration
 */

/**
 * @typedef {Object} FocusOptions
 * @property {boolean} [storeCurrent] - Whether to store current focus for restoration
 * @property {boolean} [announce] - Whether to announce the focus change
 */

/**
 * @typedef {Object} AccessibilityStatus
 * @property {boolean} reducedMotion - Whether reduced motion is preferred
 * @property {boolean} highContrast - Whether high contrast mode is active
 * @property {boolean} screenReaderActive - Whether screen reader is detected
 * @property {number} focusableElements - Number of focusable elements
 * @property {string} currentFocus - Current focus element tag name
 */

/**
 * @typedef {'polite'|'assertive'} AriaLivePriority
 */

// ------------ ACCESSIBILITY MANAGER CLASS

/**
 * Accessibility Manager Class
 *
 * Provides comprehensive accessibility features for the calculator including
 * screen reader support, keyboard navigation, and accessibility preferences.
 *
 * @class AccessibilityManager
 * @example
 * const accessibilityManager = new AccessibilityManager();
 *
 * // Announce to screen reader
 * accessibilityManager.announceToScreenReader('Calculation complete: 42');
 *
 * // Manage focus
 * accessibilityManager.manageFocus('#display', { announce: true });
 *
 * // Get accessibility status
 * const status = accessibilityManager.getAccessibilityStatus();
 */
class AccessibilityManager {
    /**
     * Create accessibility manager instance
     *
     * Initializes the accessibility manager with default settings and
     * automatically sets up all accessibility features.
     *
     * @constructor
     * @example
     * const accessibilityManager = new AccessibilityManager();
     */
    constructor() {
        /** @type {HTMLElement|null} Screen reader announcer element */
        this.announcer = null;

        /** @type {FocusHistoryItem[]} History of focus events */
        this.focusHistory = [];

        /** @type {KeyboardNavigation|null} Keyboard navigation handler */
        this.keyboardNavigation = null;

        /** @type {boolean} Whether reduced motion is preferred */
        this.reducedMotion = false;

        /** @type {boolean} Whether high contrast mode is active */
        this.highContrast = false;

        /** @type {boolean} Whether screen reader is detected (heuristic) */
        this.screenReaderActive = false;

        /** @type {MotorAccessibility} Motor accessibility features */
        this.motorAccessibility = {
            dwellTime: 1000,
            clickDelay: 0,
            dragThreshold: 10,
            largeTargets: false,
            stickyKeys: false,
            mouseKeys: false,
            switchControl: false,
            dwellControl: false
        };

        /** @type {CognitiveAccessibility} Cognitive accessibility features */
        this.cognitiveAccessibility = {
            simplifiedInterface: false,
            confirmActions: false,
            timeoutWarnings: true,
            progressIndicators: true,
            errorPrevention: true,
            contextualHelp: true,
            readingGuide: false,
            dyslexiaSupport: false
        };

        /** @type {VisualAccessibility} Visual accessibility features */
        this.visualAccessibility = {
            colorBlindnessSupport: false,
            contrastRatio: 4.5,
            fontSize: 16,
            lineHeight: 1.5,
            letterSpacing: 0,
            wordSpacing: 0,
            largeText: false,
            highContrastText: false,
            reducedTransparency: false
        };

        /** @type {AudioAccessibility} Audio accessibility features */
        this.audioAccessibility = {
            soundEnabled: true,
            hapticFeedback: true,
            visualIndicators: true,
            captionsEnabled: false,
            audioDescriptions: false
        };

        /** @type {VoiceControl} Voice control features */
        this.voiceControl = {
            enabled: false,
            commands: new Map(),
            recognition: null,
            confidence: 0.8
        };

        this.init();
    }

    // ------------ INITIALIZATION METHODS

    /**
     * Initialize accessibility features
     *
     * Sets up all accessibility components including screen reader support,
     * keyboard navigation, preference detection, and ARIA live regions.
     *
     * @method init
     * @returns {void}
     *
     * @example
     * // Called automatically in constructor
     * accessibilityManager.init();
     */
    init() {
        this.createScreenReaderAnnouncer();
        this.setupKeyboardNavigation();
        this.detectAccessibilityPreferences();
        this.setupFocusManagement();
        this.setupAriaLiveRegions();

        // Enhanced accessibility features
        this.setupMotorAccessibility();
        this.setupCognitiveAccessibility();
        this.setupVisualAccessibility();
        this.setupAudioAccessibility();
        this.setupVoiceControl();
        this.setupSkipLinks();
        this.setupLandmarks();
        this.setupColorBlindnessSupport();
        this.setupDyslexiaSupport();

        console.log('✅ Enhanced accessibility manager initialized with comprehensive disability support');
    }

    // ------------ SCREEN READER SUPPORT METHODS

    /**
     * Create screen reader announcer element
     *
     * Creates a visually hidden element for making announcements to
     * screen readers using ARIA live regions.
     *
     * @method createScreenReaderAnnouncer
     * @returns {void}
     *
     * @example
     * // Called automatically during initialization
     * accessibilityManager.createScreenReaderAnnouncer();
     */
    createScreenReaderAnnouncer() {
        /** @type {HTMLElement} */
        this.announcer = document.createElement('div');
        this.announcer.id = 'screen-reader-announcer';
        this.announcer.setAttribute('aria-live', 'polite');
        this.announcer.setAttribute('aria-atomic', 'true');
        this.announcer.className = 'visually-hidden';
        this.announcer.style.cssText = `
            position: absolute !important;
            left: -10000px !important;
            width: 1px !important;
            height: 1px !important;
            overflow: hidden !important;
        `;

        document.body.appendChild(this.announcer);
    }

    /**
     * Announce message to screen readers
     *
     * Makes announcements to screen readers using ARIA live regions
     * with configurable priority levels.
     *
     * @method announceToScreenReader
     * @param {string} message - Message to announce to screen readers
     * @param {AriaLivePriority} [priority='polite'] - Announcement priority
     * @returns {void}
     *
     * @example
     * // Polite announcement (default)
     * accessibilityManager.announceToScreenReader('Calculation complete');
     *
     * // Assertive announcement (interrupts current reading)
     * accessibilityManager.announceToScreenReader('Error occurred', 'assertive');
     */
    announceToScreenReader(message, priority = 'polite') {
        if (!this.announcer) return;

        // Clear previous message
        this.announcer.textContent = '';

        // Set priority
        this.announcer.setAttribute('aria-live', priority);

        // Announce new message with slight delay to ensure it's read
        setTimeout(() => {
            this.announcer.textContent = message;
        }, 100);

        console.log(`Screen reader announcement: ${message}`);
    }

    // ------------ KEYBOARD NAVIGATION METHODS

    /**
     * Setup keyboard navigation
     *
     * Initializes comprehensive keyboard navigation including tab navigation,
     * arrow key navigation, and calculator-specific keyboard shortcuts.
     *
     * @method setupKeyboardNavigation
     * @returns {void}
     *
     * @example
     * // Called automatically during initialization
     * accessibilityManager.setupKeyboardNavigation();
     */
    setupKeyboardNavigation() {
        /** @type {KeyboardNavigation} */
        this.keyboardNavigation = {
            currentFocus: null,
            focusableElements: [],

            updateFocusableElements: () => {
                this.keyboardNavigation.focusableElements = Array.from(
                    document.querySelectorAll(
                        'button:not([disabled]):not([tabindex="-1"]), ' +
                        'input:not([disabled]):not([tabindex="-1"]), ' +
                        '[tabindex]:not([tabindex="-1"])'
                    )
                );
            },

            handleKeyDown: (event) => {
                switch (event.key) {
                    case 'Tab':
                        this.handleTabNavigation(event);
                        break;
                    case 'ArrowUp':
                    case 'ArrowDown':
                    case 'ArrowLeft':
                    case 'ArrowRight':
                        this.handleArrowNavigation(event);
                        break;
                    case 'Enter':
                    case ' ':
                        this.handleActivation(event);
                        break;
                    case 'Escape':
                        this.handleEscape(event);
                        break;
                    default:
                        this.handleCalculatorKeys(event);
                }
            }
        };

        document.addEventListener('keydown', this.keyboardNavigation.handleKeyDown);
        document.addEventListener('focusin', this.updateCurrentFocus.bind(this));

        // Update focusable elements initially and when DOM changes
        this.keyboardNavigation.updateFocusableElements();

        // Observe DOM changes to update focusable elements
        /** @type {MutationObserver} */
        const observer = new MutationObserver(() => {
            this.keyboardNavigation.updateFocusableElements();
        });

        observer.observe(document.body, {
            childList: true,
            subtree: true,
            attributes: true,
            attributeFilter: ['disabled', 'tabindex', 'aria-hidden']
        });
    }

    // ------------ NAVIGATION HANDLER METHODS

    /**
     * Handle Tab navigation
     *
     * Manages tab navigation through focusable elements with support
     * for both forward (Tab) and backward (Shift+Tab) navigation.
     *
     * @method handleTabNavigation
     * @param {KeyboardEvent} event - Keyboard event object
     * @returns {void}
     *
     * @example
     * // Called automatically by keyboard event handler
     * accessibilityManager.handleTabNavigation(event);
     */
    handleTabNavigation(event) {
        /** @type {HTMLElement[]} */
        const focusableElements = this.keyboardNavigation.focusableElements;
        /** @type {number} */
        const currentIndex = focusableElements.indexOf(document.activeElement);

        if (event.shiftKey) {
            // Shift+Tab - go backwards
            /** @type {number} */
            const prevIndex = currentIndex <= 0 ? focusableElements.length - 1 : currentIndex - 1;
            focusableElements[prevIndex]?.focus();
        } else {
            // Tab - go forwards
            /** @type {number} */
            const nextIndex = currentIndex >= focusableElements.length - 1 ? 0 : currentIndex + 1;
            focusableElements[nextIndex]?.focus();
        }

        event.preventDefault();
    }

    /**
     * Handle arrow key navigation within calculator grid
     *
     * Provides 2D grid navigation for calculator buttons using arrow keys,
     * treating the calculator as a 4-column grid layout.
     *
     * @method handleArrowNavigation
     * @param {KeyboardEvent} event - Keyboard event object
     * @returns {void}
     *
     * @example
     * // Called automatically by keyboard event handler
     * accessibilityManager.handleArrowNavigation(event);
     */
    handleArrowNavigation(event) {
        /** @type {HTMLElement} */
        const target = event.target;

        // Only handle arrow navigation for calculator buttons
        if (!target.classList.contains('btn')) return;

        /** @type {HTMLElement[]} */
        const buttons = Array.from(document.querySelectorAll('.calculator-grid .btn'));
        /** @type {number} */
        const currentIndex = buttons.indexOf(target);

        if (currentIndex === -1) return;

        /** @type {number} */
        let newIndex;
        /** @type {number} Calculator grid is 4 columns wide */
        const gridWidth = 4;

        switch (event.key) {
            case 'ArrowUp':
                newIndex = currentIndex - gridWidth;
                break;
            case 'ArrowDown':
                newIndex = currentIndex + gridWidth;
                break;
            case 'ArrowLeft':
                newIndex = currentIndex - 1;
                break;
            case 'ArrowRight':
                newIndex = currentIndex + 1;
                break;
        }

        if (newIndex >= 0 && newIndex < buttons.length) {
            buttons[newIndex].focus();
            event.preventDefault();
        }
    }

    /**
     * Handle Enter/Space activation
     *
     * Activates buttons when Enter or Space is pressed, providing
     * keyboard accessibility for button interactions.
     *
     * @method handleActivation
     * @param {KeyboardEvent} event - Keyboard event object
     * @returns {void}
     *
     * @example
     * // Called automatically by keyboard event handler
     * accessibilityManager.handleActivation(event);
     */
    handleActivation(event) {
        /** @type {HTMLElement} */
        const target = event.target;

        if (target.tagName === 'BUTTON' && !target.disabled) {
            target.click();
            event.preventDefault();
        }
    }

    /**
     * Handle Escape key
     *
     * Provides escape key functionality to close modals, panels,
     * and other overlay elements for keyboard accessibility.
     *
     * @method handleEscape
     * @param {KeyboardEvent} event - Keyboard event object
     * @returns {void}
     *
     * @example
     * // Called automatically by keyboard event handler
     * accessibilityManager.handleEscape(event);
     */
    handleEscape(event) {
        // Close any open modals or panels
        /** @type {HTMLElement|null} */
        const modal = document.querySelector('.modal[aria-hidden="false"]');
        if (modal) {
            /** @type {HTMLElement|null} */
            const closeButton = modal.querySelector('.close-btn');
            if (closeButton) {
                closeButton.click();
            }
            event.preventDefault();
            return;
        }

        // Close scientific panel if open
        /** @type {HTMLElement|null} */
        const scientificPanel = document.getElementById('scientific-panel');
        if (scientificPanel && scientificPanel.getAttribute('aria-hidden') === 'false') {
            /** @type {HTMLElement|null} */
            const toggleButton = document.querySelector('.scientific-toggle');
            if (toggleButton) {
                toggleButton.click();
            }
            event.preventDefault();
        }
    }

    // ------------ CALCULATOR KEYBOARD SHORTCUTS

    /**
     * Handle calculator-specific keyboard shortcuts
     *
     * Provides keyboard shortcuts for calculator operations including
     * number input, operators, and special functions with screen reader announcements.
     *
     * @method handleCalculatorKeys
     * @param {KeyboardEvent} event - Keyboard event object
     * @returns {void}
     *
     * @example
     * // Called automatically by keyboard event handler
     * accessibilityManager.handleCalculatorKeys(event);
     */
    handleCalculatorKeys(event) {
        // Only handle if focus is on calculator or display
        /** @type {HTMLElement|null} */
        const calculatorMain = document.getElementById('calculator-main');
        if (!calculatorMain.contains(event.target)) return;

        /** @type {string} */
        const key = event.key;

        // Number keys
        if (/^[0-9]$/.test(key)) {
            /** @type {HTMLElement|null} */
            const button = document.querySelector(`button[aria-label*="${this.getNumberName(key)}"]`);
            if (button) {
                button.click();
                this.announceToScreenReader(`${key}`);
            }
            event.preventDefault();
            return;
        }

        // Operator keys
        /** @type {Object<string, string>} */
        const operatorMap = {
            '+': 'Add',
            '-': 'Subtract',
            '*': 'Multiply',
            '/': 'Divide',
            '=': 'Equals',
            'Enter': 'Equals',
            '.': 'Decimal point',
            'Backspace': 'Clear',
            'Delete': 'Clear all',
            'c': 'Clear all',
            'C': 'Clear all'
        };

        if (operatorMap[key]) {
            /** @type {HTMLElement|null} */
            const button = document.querySelector(`button[aria-label*="${operatorMap[key]}"]`);
            if (button) {
                button.click();
                this.announceToScreenReader(operatorMap[key]);
            }
            event.preventDefault();
        }
    }

    /**
     * Get number name for aria label matching
     *
     * Converts digit characters to their word equivalents for
     * matching against ARIA labels in button elements.
     *
     * @method getNumberName
     * @param {string} digit - Single digit character (0-9)
     * @returns {string} Word equivalent of the digit
     *
     * @example
     * const name = accessibilityManager.getNumberName('5');
     * console.log(name); // "Five"
     */
    getNumberName(digit) {
        /** @type {Object<string, string>} */
        const names = {
            '0': 'Zero',
            '1': 'One',
            '2': 'Two',
            '3': 'Three',
            '4': 'Four',
            '5': 'Five',
            '6': 'Six',
            '7': 'Seven',
            '8': 'Eight',
            '9': 'Nine'
        };
        return names[digit] || digit;
    }

    // ------------ FOCUS MANAGEMENT METHODS

    /**
     * Update current focus tracking
     *
     * Tracks focus changes and maintains a history of focused elements
     * for restoration and navigation purposes.
     *
     * @method updateCurrentFocus
     * @param {FocusEvent} event - Focus event object
     * @returns {void}
     *
     * @example
     * // Called automatically by focus event listener
     * accessibilityManager.updateCurrentFocus(event);
     */
    updateCurrentFocus(event) {
        this.keyboardNavigation.currentFocus = event.target;

        // Store focus history for restoration
        this.focusHistory.push({
            element: event.target,
            timestamp: Date.now()
        });

        // Keep only last 10 focus events
        if (this.focusHistory.length > 10) {
            this.focusHistory.shift();
        }
    }

    /**
     * Manage focus for dynamic content
     *
     * Programmatically manages focus with options for storing current focus
     * and announcing focus changes to screen readers.
     *
     * @method manageFocus
     * @param {string|HTMLElement} target - CSS selector or element to focus
     * @param {FocusOptions} [options={}] - Focus management options
     * @returns {void}
     *
     * @example
     * // Focus with announcement
     * accessibilityManager.manageFocus('#display', {
     *   announce: true,
     *   storeCurrent: true
     * });
     *
     * // Focus element directly
     * const button = document.querySelector('.equals-btn');
     * accessibilityManager.manageFocus(button);
     */
    manageFocus(target, options = {}) {
        /** @type {HTMLElement|null} */
        const element = typeof target === 'string' ? document.querySelector(target) : target;

        if (!element) {
            console.warn('Focus target not found:', target);
            return;
        }

        // Store current focus for restoration
        if (options.storeCurrent && document.activeElement) {
            this.focusHistory.push({
                element: document.activeElement,
                timestamp: Date.now(),
                stored: true
            });
        }

        // Focus the target element
        element.focus();

        // Announce focus change if requested
        if (options.announce) {
            /** @type {string} */
            const label = element.getAttribute('aria-label') || element.textContent || 'element';
            this.announceToScreenReader(`Focused on ${label}`);
        }
    }

    /**
     * Restore previous focus
     *
     * Restores focus to the last explicitly stored focus position,
     * useful for returning focus after modal or panel interactions.
     *
     * @method restoreFocus
     * @returns {void}
     *
     * @example
     * // After closing a modal
     * accessibilityManager.restoreFocus();
     */
    restoreFocus() {
        /** @type {FocusHistoryItem|undefined} */
        const lastStored = this.focusHistory.reverse().find(item => item.stored);

        if (lastStored && lastStored.element) {
            lastStored.element.focus();
            this.announceToScreenReader('Focus restored');
        }
    }

    // ------------ ARIA LIVE REGIONS SETUP

    /**
     * Setup ARIA live regions
     *
     * Configures ARIA live regions for calculator display and history
     * to ensure screen readers announce changes appropriately.
     *
     * @method setupAriaLiveRegions
     * @returns {void}
     *
     * @example
     * // Called automatically during initialization
     * accessibilityManager.setupAriaLiveRegions();
     */
    setupAriaLiveRegions() {
        // Ensure display has proper ARIA attributes
        /** @type {HTMLElement|null} */
        const display = document.getElementById('display');
        if (display) {
            display.setAttribute('aria-live', 'polite');
            display.setAttribute('aria-atomic', 'true');
        }

        // Ensure history has proper ARIA attributes
        /** @type {HTMLElement|null} */
        const history = document.getElementById('history');
        if (history) {
            history.setAttribute('aria-live', 'polite');
            history.setAttribute('aria-atomic', 'false');
        }
    }

    // ------------ ACCESSIBILITY PREFERENCE DETECTION

    /**
     * Detect accessibility preferences
     *
     * Detects user accessibility preferences including reduced motion,
     * high contrast mode, and screen reader usage using media queries and heuristics.
     *
     * @method detectAccessibilityPreferences
     * @returns {void}
     *
     * @example
     * // Called automatically during initialization
     * accessibilityManager.detectAccessibilityPreferences();
     */
    detectAccessibilityPreferences() {
        // Detect reduced motion preference
        if (window.matchMedia) {
            /** @type {MediaQueryList} */
            const reducedMotionQuery = window.matchMedia('(prefers-reduced-motion: reduce)');
            this.reducedMotion = reducedMotionQuery.matches;

            reducedMotionQuery.addEventListener('change', (e) => {
                this.reducedMotion = e.matches;
                this.applyReducedMotion(e.matches);
            });

            // Detect high contrast mode
            /** @type {MediaQueryList} */
            const highContrastQuery = window.matchMedia('(forced-colors: active)');
            this.highContrast = highContrastQuery.matches;

            highContrastQuery.addEventListener('change', (e) => {
                this.highContrast = e.matches;
                this.applyHighContrast(e.matches);
            });
        }

        // Detect screen reader (heuristic)
        this.detectScreenReader();
    }

    /**
     * Apply reduced motion styles
     *
     * Applies or removes reduced motion styling based on user preference,
     * affecting animations and transitions throughout the calculator.
     *
     * @method applyReducedMotion
     * @param {boolean} enabled - Whether reduced motion should be enabled
     * @returns {void}
     *
     * @example
     * accessibilityManager.applyReducedMotion(true);
     */
    applyReducedMotion(enabled) {
        document.documentElement.classList.toggle('reduced-motion', enabled);

        if (enabled) {
            this.announceToScreenReader('Reduced motion enabled');
        }
    }

    /**
     * Apply high contrast styles
     *
     * Applies or removes high contrast styling based on system preference,
     * enhancing visibility for users with visual impairments.
     *
     * @method applyHighContrast
     * @param {boolean} enabled - Whether high contrast mode should be enabled
     * @returns {void}
     *
     * @example
     * accessibilityManager.applyHighContrast(true);
     */
    applyHighContrast(enabled) {
        document.documentElement.classList.toggle('high-contrast', enabled);

        if (enabled) {
            this.announceToScreenReader('High contrast mode detected');
        }
    }

    /**
     * Detect screen reader (heuristic approach)
     *
     * Attempts to detect screen reader usage using heuristic methods.
     * Note: This is not 100% reliable and should be used as a hint only.
     *
     * @method detectScreenReader
     * @returns {void}
     *
     * @example
     * // Called automatically during initialization
     * accessibilityManager.detectScreenReader();
     */
    detectScreenReader() {
        // This is a heuristic and not 100% reliable
        /** @type {HTMLElement} */
        const testElement = document.createElement('div');
        testElement.setAttribute('aria-hidden', 'true');
        testElement.style.cssText = 'position: absolute; left: -10000px;';
        testElement.textContent = 'Screen reader test';

        document.body.appendChild(testElement);

        setTimeout(() => {
            // If the element is being read by a screen reader,
            // it might affect focus or other properties
            this.screenReaderActive = document.activeElement === testElement;
            document.body.removeChild(testElement);
        }, 100);
    }

    // ------------ ADDITIONAL FOCUS MANAGEMENT

    /**
     * Setup focus management
     *
     * Sets up focus trapping for modals and other overlay elements
     * to ensure keyboard navigation stays within the active context.
     *
     * @method setupFocusManagement
     * @returns {void}
     *
     * @example
     * // Called automatically during initialization
     * accessibilityManager.setupFocusManagement();
     */
    setupFocusManagement() {
        // Trap focus in modals
        document.addEventListener('focusin', (event) => {
            /** @type {HTMLElement|null} */
            const modal = document.querySelector('.modal[aria-hidden="false"]');
            if (modal && !modal.contains(event.target)) {
                /** @type {HTMLElement|null} */
                const firstFocusable = modal.querySelector('button, input, [tabindex]:not([tabindex="-1"])');
                if (firstFocusable) {
                    firstFocusable.focus();
                }
            }
        });
    }

    // ------------ ARIA LIVE REGION UPDATES

    /**
     * Update ARIA live region
     *
     * Updates an ARIA live region with a new message and priority level
     * for dynamic content announcements.
     *
     * @method updateAriaLive
     * @param {string} elementId - ID of the element to update
     * @param {string} message - Message to announce
     * @param {AriaLivePriority} [priority='polite'] - Announcement priority
     * @returns {void}
     *
     * @example
     * accessibilityManager.updateAriaLive('display', 'Result: 42', 'polite');
     * accessibilityManager.updateAriaLive('error-region', 'Error occurred', 'assertive');
     */
    updateAriaLive(elementId, message, priority = 'polite') {
        /** @type {HTMLElement|null} */
        const element = document.getElementById(elementId);
        if (element) {
            element.setAttribute('aria-live', priority);
            element.textContent = message;
        }
    }

    // ------------ STATUS AND MONITORING

    /**
     * Get accessibility status
     *
     * Returns comprehensive information about the current accessibility
     * state including preferences, focus information, and feature status.
     *
     * @method getAccessibilityStatus
     * @returns {AccessibilityStatus} Current accessibility status
     *
     * @example
     * const status = accessibilityManager.getAccessibilityStatus();
     * console.log('Reduced motion:', status.reducedMotion);
     * console.log('Screen reader active:', status.screenReaderActive);
     * console.log('Focusable elements:', status.focusableElements);
     */
    getAccessibilityStatus() {
        return {
            reducedMotion: this.reducedMotion,
            highContrast: this.highContrast,
            screenReaderActive: this.screenReaderActive,
            focusableElements: this.keyboardNavigation.focusableElements.length,
            currentFocus: this.keyboardNavigation.currentFocus?.tagName || 'none',
            motorAccessibility: this.motorAccessibility,
            cognitiveAccessibility: this.cognitiveAccessibility,
            visualAccessibility: this.visualAccessibility,
            audioAccessibility: this.audioAccessibility,
            voiceControl: this.voiceControl.enabled
        };
    }

    // ------------ MOTOR ACCESSIBILITY METHODS

    /**
     * Setup motor accessibility features
     *
     * Configures accessibility features for users with motor disabilities
     * including dwell control, switch control, and large target areas.
     *
     * @method setupMotorAccessibility
     * @returns {void}
     */
    setupMotorAccessibility() {
        // Detect if user prefers reduced motion
        const prefersReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)').matches;
        if (prefersReducedMotion) {
            this.motorAccessibility.reducedMotion = true;
            document.documentElement.classList.add('reduced-motion');
        }

        // Setup dwell control (hover to click)
        this.setupDwellControl();

        // Setup large target areas
        this.setupLargeTargets();

        // Setup sticky keys simulation
        this.setupStickyKeys();

        console.log('🦾 Motor accessibility features configured');
    }

    /**
     * Setup dwell control for hands-free interaction
     *
     * Allows users to activate buttons by hovering for a specified duration,
     * useful for users who cannot click but can control cursor movement.
     *
     * @method setupDwellControl
     * @returns {void}
     */
    setupDwellControl() {
        let dwellTimer = null;
        let currentTarget = null;

        const handleMouseEnter = (event) => {
            if (!this.motorAccessibility.dwellControl) return;

            const target = event.target;
            if (target.tagName === 'BUTTON' && !target.disabled) {
                currentTarget = target;
                target.classList.add('dwell-hover');

                dwellTimer = setTimeout(() => {
                    if (currentTarget === target) {
                        target.click();
                        this.announceToScreenReader(`Activated ${target.getAttribute('aria-label') || target.textContent}`);
                    }
                }, this.motorAccessibility.dwellTime);
            }
        };

        const handleMouseLeave = (event) => {
            if (dwellTimer) {
                clearTimeout(dwellTimer);
                dwellTimer = null;
            }
            event.target.classList.remove('dwell-hover');
            currentTarget = null;
        };

        // Add dwell control to all buttons
        document.addEventListener('mouseenter', handleMouseEnter, true);
        document.addEventListener('mouseleave', handleMouseLeave, true);
    }

    /**
     * Setup large target areas for easier interaction
     *
     * Increases the size of interactive elements to meet WCAG guidelines
     * for users with motor disabilities.
     *
     * @method setupLargeTargets
     * @returns {void}
     */
    setupLargeTargets() {
        if (this.motorAccessibility.largeTargets) {
            document.documentElement.classList.add('large-targets');

            // Ensure minimum 44px touch targets
            const style = document.createElement('style');
            style.textContent = `
                .large-targets button,
                .large-targets input,
                .large-targets [role="button"] {
                    min-width: 44px !important;
                    min-height: 44px !important;
                    padding: 12px !important;
                }
            `;
            document.head.appendChild(style);
        }
    }

    /**
     * Setup sticky keys functionality
     *
     * Allows modifier keys to remain active without holding them down,
     * useful for users who cannot press multiple keys simultaneously.
     *
     * @method setupStickyKeys
     * @returns {void}
     */
    setupStickyKeys() {
        if (!this.motorAccessibility.stickyKeys) return;

        const stickyState = {
            shift: false,
            ctrl: false,
            alt: false
        };

        document.addEventListener('keydown', (event) => {
            // Toggle sticky keys on double-tap
            if (event.key === 'Shift' && event.repeat) {
                stickyState.shift = !stickyState.shift;
                this.announceToScreenReader(`Shift key ${stickyState.shift ? 'locked' : 'unlocked'}`);
                event.preventDefault();
            }
        });
    }

    // ------------ COGNITIVE ACCESSIBILITY METHODS

    /**
     * Setup cognitive accessibility features
     *
     * Configures features to support users with cognitive disabilities
     * including simplified interface, confirmation dialogs, and contextual help.
     *
     * @method setupCognitiveAccessibility
     * @returns {void}
     */
    setupCognitiveAccessibility() {
        // Setup simplified interface mode
        if (this.cognitiveAccessibility.simplifiedInterface) {
            this.enableSimplifiedInterface();
        }

        // Setup action confirmations
        if (this.cognitiveAccessibility.confirmActions) {
            this.setupActionConfirmations();
        }

        // Setup contextual help
        if (this.cognitiveAccessibility.contextualHelp) {
            this.setupContextualHelp();
        }

        // Setup timeout warnings
        if (this.cognitiveAccessibility.timeoutWarnings) {
            this.setupTimeoutWarnings();
        }

        console.log('🧠 Cognitive accessibility features configured');
    }

    /**
     * Enable simplified interface mode
     *
     * Reduces visual complexity and provides clearer navigation
     * for users with cognitive disabilities.
     *
     * @method enableSimplifiedInterface
     * @returns {void}
     */
    enableSimplifiedInterface() {
        document.documentElement.classList.add('simplified-interface');

        // Hide non-essential elements
        const nonEssential = document.querySelectorAll('.advanced-features, .decorative');
        nonEssential.forEach(element => {
            element.setAttribute('aria-hidden', 'true');
            element.style.display = 'none';
        });
    }

    /**
     * Setup action confirmations
     *
     * Adds confirmation dialogs for destructive actions to prevent
     * accidental operations.
     *
     * @method setupActionConfirmations
     * @returns {void}
     */
    setupActionConfirmations() {
        const destructiveActions = document.querySelectorAll('[data-destructive="true"], .clear-all, .reset');

        destructiveActions.forEach(button => {
            const originalHandler = button.onclick;
            button.onclick = (event) => {
                event.preventDefault();

                const action = button.getAttribute('aria-label') || button.textContent;
                const confirmed = confirm(`Are you sure you want to ${action.toLowerCase()}?`);

                if (confirmed && originalHandler) {
                    originalHandler.call(button, event);
                } else if (!confirmed) {
                    this.announceToScreenReader('Action cancelled');
                }
            };
        });
    }

    /**
     * Setup contextual help system
     *
     * Provides context-sensitive help and instructions for users
     * who need additional guidance.
     *
     * @method setupContextualHelp
     * @returns {void}
     */
    setupContextualHelp() {
        // Add help indicators to complex elements
        const complexElements = document.querySelectorAll('.calculator-display, .scientific-functions');

        complexElements.forEach(element => {
            const helpIcon = document.createElement('button');
            helpIcon.className = 'help-icon';
            helpIcon.setAttribute('aria-label', 'Get help for this feature');
            helpIcon.innerHTML = '?';
            helpIcon.style.cssText = `
                position: absolute;
                top: 5px;
                right: 5px;
                width: 20px;
                height: 20px;
                border-radius: 50%;
                background: var(--accent-color);
                color: white;
                border: none;
                font-size: 12px;
                cursor: pointer;
            `;

            element.style.position = 'relative';
            element.appendChild(helpIcon);

            helpIcon.addEventListener('click', () => {
                const helpText = this.getContextualHelp(element);
                this.announceToScreenReader(helpText);
            });
        });
    }

    /**
     * Get contextual help text for an element
     *
     * @method getContextualHelp
     * @param {HTMLElement} element - Element to get help for
     * @returns {string} Help text
     * @private
     */
    getContextualHelp(element) {
        if (element.classList.contains('calculator-display')) {
            return 'This display shows your current calculation and results. Numbers and operations appear here as you enter them.';
        }

        if (element.classList.contains('scientific-functions')) {
            return 'These are advanced mathematical functions like sine, cosine, logarithm, and square root.';
        }

        return 'This is an interactive calculator element. Use keyboard or mouse to interact with it.';
    }

    /**
     * Setup timeout warnings
     *
     * Warns users before automatic timeouts occur, giving them
     * a chance to extend their session.
     *
     * @method setupTimeoutWarnings
     * @returns {void}
     */
    setupTimeoutWarnings() {
        let timeoutWarning = null;
        let sessionTimeout = null;

        const resetTimeout = () => {
            if (timeoutWarning) clearTimeout(timeoutWarning);
            if (sessionTimeout) clearTimeout(sessionTimeout);

            // Warn 30 seconds before timeout
            timeoutWarning = setTimeout(() => {
                this.announceToScreenReader('Your session will expire in 30 seconds. Press any key to continue.');

                sessionTimeout = setTimeout(() => {
                    this.announceToScreenReader('Session expired. Calculator has been reset.');
                    // Reset calculator state
                    const clearButton = document.querySelector('[aria-label*="Clear all"]');
                    if (clearButton) clearButton.click();
                }, 30000);
            }, 5 * 60 * 1000 - 30000); // 4.5 minutes
        };

        // Reset timeout on any user interaction
        document.addEventListener('click', resetTimeout);
        document.addEventListener('keydown', resetTimeout);

        resetTimeout(); // Start initial timeout
    }

    // ------------ VISUAL ACCESSIBILITY METHODS

    /**
     * Setup visual accessibility features
     *
     * Configures features for users with visual impairments including
     * high contrast, large text, and color blindness support.
     *
     * @method setupVisualAccessibility
     * @returns {void}
     */
    setupVisualAccessibility() {
        // Detect high contrast preference
        const prefersHighContrast = window.matchMedia('(prefers-contrast: high)').matches;
        if (prefersHighContrast) {
            this.visualAccessibility.highContrastText = true;
            document.documentElement.classList.add('high-contrast');
        }

        // Setup large text support
        this.setupLargeTextSupport();

        // Setup color blindness support
        this.setupColorBlindnessSupport();

        // Setup reduced transparency
        this.setupReducedTransparency();

        console.log('👁️ Visual accessibility features configured');
    }

    /**
     * Setup large text support
     *
     * Provides options for users who need larger text sizes
     * for better readability.
     *
     * @method setupLargeTextSupport
     * @returns {void}
     */
    setupLargeTextSupport() {
        if (this.visualAccessibility.largeText) {
            document.documentElement.classList.add('large-text');

            const style = document.createElement('style');
            style.textContent = `
                .large-text {
                    font-size: ${this.visualAccessibility.fontSize * 1.25}px !important;
                    line-height: ${this.visualAccessibility.lineHeight * 1.2} !important;
                }
                .large-text button {
                    font-size: ${this.visualAccessibility.fontSize * 1.5}px !important;
                    padding: 16px !important;
                }
                .large-text .calculator-display {
                    font-size: ${this.visualAccessibility.fontSize * 2}px !important;
                }
            `;
            document.head.appendChild(style);
        }
    }

    /**
     * Setup color blindness support
     *
     * Provides alternative visual indicators for users with
     * color vision deficiencies.
     *
     * @method setupColorBlindnessSupport
     * @returns {void}
     */
    setupColorBlindnessSupport() {
        if (!this.visualAccessibility.colorBlindnessSupport) return;

        document.documentElement.classList.add('colorblind-support');

        // Add patterns and shapes to color-coded elements
        const style = document.createElement('style');
        style.textContent = `
            .colorblind-support .btn-operator {
                background-image: repeating-linear-gradient(
                    45deg,
                    transparent,
                    transparent 2px,
                    rgba(255,255,255,0.1) 2px,
                    rgba(255,255,255,0.1) 4px
                );
            }
            .colorblind-support .btn-number {
                border: 2px solid currentColor;
            }
            .colorblind-support .btn-equals {
                background-image: radial-gradient(circle, rgba(255,255,255,0.1) 1px, transparent 1px);
                background-size: 8px 8px;
            }
        `;
        document.head.appendChild(style);
    }

    /**
     * Setup reduced transparency
     *
     * Reduces or removes transparency effects for users who
     * have difficulty with transparent elements.
     *
     * @method setupReducedTransparency
     * @returns {void}
     */
    setupReducedTransparency() {
        const prefersReducedTransparency = window.matchMedia('(prefers-reduced-transparency: reduce)').matches;

        if (prefersReducedTransparency || this.visualAccessibility.reducedTransparency) {
            document.documentElement.classList.add('reduced-transparency');

            const style = document.createElement('style');
            style.textContent = `
                .reduced-transparency * {
                    opacity: 1 !important;
                    background-color: var(--bg-color) !important;
                }
            `;
            document.head.appendChild(style);
        }
    }

    // ------------ AUDIO ACCESSIBILITY METHODS

    /**
     * Setup audio accessibility features
     *
     * Configures audio feedback and haptic alternatives for users
     * with hearing impairments.
     *
     * @method setupAudioAccessibility
     * @returns {void}
     */
    setupAudioAccessibility() {
        // Setup haptic feedback for mobile devices
        this.setupHapticFeedback();

        // Setup visual indicators for audio cues
        this.setupVisualIndicators();

        console.log('🔊 Audio accessibility features configured');
    }

    /**
     * Setup haptic feedback
     *
     * Provides tactile feedback for users who cannot hear audio cues.
     *
     * @method setupHapticFeedback
     * @returns {void}
     */
    setupHapticFeedback() {
        if (!this.audioAccessibility.hapticFeedback) return;

        const buttons = document.querySelectorAll('button');
        buttons.forEach(button => {
            button.addEventListener('click', () => {
                // Vibrate on button press (mobile devices)
                if (navigator.vibrate) {
                    navigator.vibrate(50);
                }
            });
        });
    }

    /**
     * Setup visual indicators
     *
     * Provides visual feedback for audio cues and alerts.
     *
     * @method setupVisualIndicators
     * @returns {void}
     */
    setupVisualIndicators() {
        if (!this.audioAccessibility.visualIndicators) return;

        // Create visual feedback for button presses
        const style = document.createElement('style');
        style.textContent = `
            .visual-feedback {
                animation: flash 0.2s ease-in-out;
            }
            @keyframes flash {
                0%, 100% { background-color: var(--btn-bg); }
                50% { background-color: var(--accent-color); }
            }
        `;
        document.head.appendChild(style);

        // Add visual feedback to buttons
        document.addEventListener('click', (event) => {
            if (event.target.tagName === 'BUTTON') {
                event.target.classList.add('visual-feedback');
                setTimeout(() => {
                    event.target.classList.remove('visual-feedback');
                }, 200);
            }
        });
    }

    // ------------ VOICE CONTROL METHODS

    /**
     * Setup voice control features
     *
     * Configures speech recognition for hands-free calculator operation.
     *
     * @method setupVoiceControl
     * @returns {void}
     */
    setupVoiceControl() {
        if (!('webkitSpeechRecognition' in window) && !('SpeechRecognition' in window)) {
            console.warn('Speech recognition not supported in this browser');
            return;
        }

        this.initializeSpeechRecognition();
        this.setupVoiceCommands();

        console.log('🎤 Voice control features configured');
    }

    /**
     * Initialize speech recognition
     *
     * Sets up the Web Speech API for voice input.
     *
     * @method initializeSpeechRecognition
     * @returns {void}
     * @private
     */
    initializeSpeechRecognition() {
        const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
        this.voiceControl.recognition = new SpeechRecognition();

        this.voiceControl.recognition.continuous = true;
        this.voiceControl.recognition.interimResults = false;
        this.voiceControl.recognition.lang = 'en-US';

        this.voiceControl.recognition.onresult = (event) => {
            const command = event.results[event.results.length - 1][0].transcript.toLowerCase().trim();
            this.processVoiceCommand(command);
        };

        this.voiceControl.recognition.onerror = (event) => {
            console.warn('Speech recognition error:', event.error);
            this.announceToScreenReader('Voice recognition error. Please try again.');
        };
    }

    /**
     * Setup voice commands mapping
     *
     * Defines the voice commands and their corresponding actions.
     *
     * @method setupVoiceCommands
     * @returns {void}
     * @private
     */
    setupVoiceCommands() {
        // Number commands
        for (let i = 0; i <= 9; i++) {
            this.voiceControl.commands.set(i.toString(), () => this.clickButton(`[aria-label*="${this.getNumberName(i.toString())}"]`));
            this.voiceControl.commands.set(this.getNumberName(i.toString()).toLowerCase(), () => this.clickButton(`[aria-label*="${this.getNumberName(i.toString())}"]`));
        }

        // Operation commands
        this.voiceControl.commands.set('plus', () => this.clickButton('[aria-label*="Add"]'));
        this.voiceControl.commands.set('add', () => this.clickButton('[aria-label*="Add"]'));
        this.voiceControl.commands.set('minus', () => this.clickButton('[aria-label*="Subtract"]'));
        this.voiceControl.commands.set('subtract', () => this.clickButton('[aria-label*="Subtract"]'));
        this.voiceControl.commands.set('times', () => this.clickButton('[aria-label*="Multiply"]'));
        this.voiceControl.commands.set('multiply', () => this.clickButton('[aria-label*="Multiply"]'));
        this.voiceControl.commands.set('divide', () => this.clickButton('[aria-label*="Divide"]'));
        this.voiceControl.commands.set('equals', () => this.clickButton('[aria-label*="Equals"]'));
        this.voiceControl.commands.set('calculate', () => this.clickButton('[aria-label*="Equals"]'));
        this.voiceControl.commands.set('clear', () => this.clickButton('[aria-label*="Clear all"]'));
        this.voiceControl.commands.set('decimal', () => this.clickButton('[aria-label*="Decimal"]'));
        this.voiceControl.commands.set('point', () => this.clickButton('[aria-label*="Decimal"]'));
    }

    /**
     * Process voice command
     *
     * Interprets and executes voice commands.
     *
     * @method processVoiceCommand
     * @param {string} command - Voice command to process
     * @returns {void}
     * @private
     */
    processVoiceCommand(command) {
        console.log('Voice command received:', command);

        if (this.voiceControl.commands.has(command)) {
            this.voiceControl.commands.get(command)();
            this.announceToScreenReader(`Executed: ${command}`);
        } else {
            // Try to parse complex commands like "five plus three equals"
            this.parseComplexCommand(command);
        }
    }

    /**
     * Parse complex voice commands
     *
     * Handles multi-part voice commands like "five plus three equals".
     *
     * @method parseComplexCommand
     * @param {string} command - Complex command to parse
     * @returns {void}
     * @private
     */
    parseComplexCommand(command) {
        const words = command.split(' ');
        let executed = false;

        for (const word of words) {
            if (this.voiceControl.commands.has(word)) {
                this.voiceControl.commands.get(word)();
                executed = true;
            }
        }

        if (executed) {
            this.announceToScreenReader(`Executed command: ${command}`);
        } else {
            this.announceToScreenReader('Command not recognized. Try saying numbers, operations, or "clear".');
        }
    }

    /**
     * Click button by selector
     *
     * Helper method to click buttons based on CSS selector.
     *
     * @method clickButton
     * @param {string} selector - CSS selector for button
     * @returns {void}
     * @private
     */
    clickButton(selector) {
        const button = document.querySelector(selector);
        if (button && !button.disabled) {
            button.click();
        }
    }

    /**
     * Toggle voice control
     *
     * Enables or disables voice control functionality.
     *
     * @method toggleVoiceControl
     * @param {boolean} enabled - Whether to enable voice control
     * @returns {void}
     */
    toggleVoiceControl(enabled) {
        this.voiceControl.enabled = enabled;

        if (enabled && this.voiceControl.recognition) {
            this.voiceControl.recognition.start();
            this.announceToScreenReader('Voice control enabled. You can now speak calculator commands.');
        } else if (this.voiceControl.recognition) {
            this.voiceControl.recognition.stop();
            this.announceToScreenReader('Voice control disabled.');
        }
    }

    // ------------ SKIP LINKS AND LANDMARKS

    /**
     * Setup skip links for keyboard navigation
     *
     * Creates skip links to help keyboard users navigate quickly
     * to main content areas.
     *
     * @method setupSkipLinks
     * @returns {void}
     */
    setupSkipLinks() {
        const skipLinksContainer = document.createElement('div');
        skipLinksContainer.className = 'skip-links';
        skipLinksContainer.innerHTML = `
            <a href="#calculator-display" class="skip-link">Skip to calculator display</a>
            <a href="#calculator-buttons" class="skip-link">Skip to calculator buttons</a>
            <a href="#calculator-history" class="skip-link">Skip to calculation history</a>
        `;

        // Style skip links
        const style = document.createElement('style');
        style.textContent = `
            .skip-links {
                position: absolute;
                top: -40px;
                left: 6px;
                z-index: 1000;
            }
            .skip-link {
                position: absolute;
                left: -10000px;
                top: auto;
                width: 1px;
                height: 1px;
                overflow: hidden;
                background: var(--accent-color);
                color: white;
                padding: 8px 16px;
                text-decoration: none;
                border-radius: 4px;
            }
            .skip-link:focus {
                position: static;
                width: auto;
                height: auto;
                left: auto;
                top: auto;
            }
        `;
        document.head.appendChild(style);

        document.body.insertBefore(skipLinksContainer, document.body.firstChild);
    }

    /**
     * Setup ARIA landmarks
     *
     * Adds proper landmark roles to help screen reader users
     * navigate the calculator interface.
     *
     * @method setupLandmarks
     * @returns {void}
     */
    setupLandmarks() {
        // Add main landmark
        const calculatorMain = document.getElementById('calculator-main') || document.querySelector('.calculator');
        if (calculatorMain) {
            calculatorMain.setAttribute('role', 'main');
            calculatorMain.setAttribute('aria-label', 'Calculator application');
        }

        // Add region landmarks
        const display = document.getElementById('calculator-display') || document.querySelector('.calculator-display');
        if (display) {
            display.setAttribute('role', 'region');
            display.setAttribute('aria-label', 'Calculator display and current calculation');
        }

        const buttons = document.getElementById('calculator-buttons') || document.querySelector('.calculator-buttons');
        if (buttons) {
            buttons.setAttribute('role', 'region');
            buttons.setAttribute('aria-label', 'Calculator input buttons');
        }

        const history = document.getElementById('calculator-history') || document.querySelector('.calculator-history');
        if (history) {
            history.setAttribute('role', 'region');
            history.setAttribute('aria-label', 'Calculation history');
        }
    }

    // ------------ DYSLEXIA SUPPORT

    /**
     * Setup dyslexia support features
     *
     * Configures features to help users with dyslexia including
     * dyslexia-friendly fonts and reading aids.
     *
     * @method setupDyslexiaSupport
     * @returns {void}
     */
    setupDyslexiaSupport() {
        if (!this.cognitiveAccessibility.dyslexiaSupport) return;

        document.documentElement.classList.add('dyslexia-support');

        const style = document.createElement('style');
        style.textContent = `
            .dyslexia-support {
                font-family: 'OpenDyslexic', 'Comic Sans MS', cursive !important;
                letter-spacing: 0.1em !important;
                word-spacing: 0.2em !important;
                line-height: 1.8 !important;
            }
            .dyslexia-support button {
                font-weight: bold !important;
                border: 2px solid currentColor !important;
            }
            .dyslexia-support .calculator-display {
                background: #f0f0f0 !important;
                color: #333 !important;
                font-size: 1.5em !important;
            }
        `;
        document.head.appendChild(style);

        console.log('📖 Dyslexia support features configured');
    }
}

// ------------ MODULE EXPORTS AND GLOBAL REGISTRATION

/**
 * Export for CommonJS module systems (Node.js, older bundlers)
 * Provides compatibility with various module loading systems.
 *
 * @module AccessibilityManager
 * @type {typeof AccessibilityManager}
 */
if (typeof module !== 'undefined' && module.exports) {
    module.exports = AccessibilityManager;
} else if (typeof window !== 'undefined') {
    /**
     * Make AccessibilityManager available globally for legacy compatibility
     * and direct script inclusion scenarios.
     *
     * @global
     * @type {typeof AccessibilityManager}
     */
    window.AccessibilityManager = AccessibilityManager;
}

/**
 * Export for ES6 module systems (modern bundlers, native ES modules)
 * Enables tree-shaking and modern import/export syntax.
 *
 * @default AccessibilityManager
 */
export default AccessibilityManager;
