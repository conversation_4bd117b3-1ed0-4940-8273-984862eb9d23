/**
 * @file <PERSON><PERSON> UTILITIES MODULE
 *
 * @version 1.0.0
 * <AUTHOR>
 * @contributors
 * @license MIT
 *
 * @description
 * Progressive Web App utilities module for The Great Calculator.
 * Provides PWA functionality including installation detection,
 * network monitoring, install prompts, update detection, and storage management.
 *
 * Features:
 * - App installation detection and prompts
 * - Network connectivity monitoring
 * - Service worker update detection
 * - Web Share API integration
 * - Storage quota management
 * - Platform-specific install instructions
 * - PWA analytics and event logging
 *
 * @requires Web APIs: Service Worker, Web Share, Storage API
 */

// ------------ TYPE AND JSDOC DEFINITIONS

/**
 * @typedef {Object} PWAInstallPrompt
 * @property {Function} prompt - Show install prompt
 * @property {Promise<{outcome: string}>} userChoice - User choice promise
 */

/**
 * @typedef {Object} PWAAppInfo
 * @property {boolean} isInstalled - Whether app is installed
 * @property {boolean} isOnline - Current network status
 * @property {boolean} canInstall - Whether app can be installed
 * @property {boolean} updateAvailable - Whether update is available
 * @property {boolean} standalone - Whether running in standalone mode
 * @property {string} userAgent - Browser user agent string
 * @property {string} platform - Platform identifier
 */

/**
 * @typedef {Object} PWAStorageInfo
 * @property {number} quota - Total storage quota in bytes
 * @property {number} usage - Current storage usage in bytes
 * @property {number} available - Available storage in bytes
 * @property {number} percentage - Usage percentage (0-100)
 */

/**
 * @typedef {Object} PWAInstallInstructions
 * @property {string} platform - Platform name (iOS, Android, Desktop)
 * @property {string} instructions - Installation instructions text
 * @property {string} icon - Platform icon emoji
 */

/**
 * @typedef {Object} PWAShareData
 * @property {string} [title] - Title to share
 * @property {string} [text] - Text content to share
 * @property {string} [url] - URL to share
 */

/**
 * @typedef {Object} PWAEventData
 * @property {string} event - Event name
 * @property {string} timestamp - ISO timestamp
 * @property {PWAAppInfo} appInfo - Current app information
 * @property {Object} [data] - Additional event data
 */

// ------------ PWA UTILITIES CLASS

/**
 * PWA Utilities Class
 *
 * Provides comprehensive Progressive Web App functionality for
 * user experience including installation management, network monitoring,
 * and storage utilities.
 *
 * @class PWAUtils
 * @example
 * const pwaUtils = new PWAUtils();
 *
 * // Check if app can be installed
 * if (pwaUtils.canInstall()) {
 *   await pwaUtils.promptInstall();
 * }
 *
 * // Monitor network changes
 * window.addEventListener('pwa-network-change', (event) => {
 *   console.log('Network status:', event.detail.status);
 * });
 */
class PWAUtils {
    /**
     * Create PWA utilities instance
     *
     * Initializes PWA utilities with default state and sets up
     * all necessary event listeners and detection mechanisms.
     *
     * @constructor
     * @example
     * const pwaUtils = new PWAUtils();
     */
    constructor() {
        /** @type {boolean} Whether the app is currently installed */
        this.isInstalled = false;

        /** @type {boolean} Current network connectivity status */
        this.isOnline = navigator.onLine;

        /** @type {PWAInstallPrompt|null} Deferred install prompt event */
        this.installPrompt = null;

        /** @type {boolean} Whether a service worker update is available */
        this.updateAvailable = false;

        this.init();
    }

    // ------------ INITIALIZATION METHODS

    /**
     * Initialize PWA utilities
     *
     * Sets up all PWA functionality including installation detection,
     * network monitoring, install prompts, and update detection.
     *
     * @method init
     * @returns {void}
     *
     * @example
     * const pwaUtils = new PWAUtils();
     * // init() is called automatically in constructor
     */
    init() {
        this.detectInstallation();
        this.setupNetworkDetection();
        this.setupInstallPrompt();
        this.setupUpdateDetection();

        console.log('PWA Utils initialized');
    }

    /**
     * Detect if app is installed
     *
     * Checks various indicators to determine if the PWA is currently
     * installed on the user's device, including standalone mode and
     * platform-specific indicators.
     *
     * @method detectInstallation
     * @returns {boolean} True if app appears to be installed
     *
     * @example
     * const isInstalled = pwaUtils.detectInstallation();
     * console.log('App installed:', isInstalled);
     */
    detectInstallation() {
        // Check if running in standalone mode
        this.isInstalled = window.matchMedia('(display-mode: standalone)').matches ||
                          window.navigator.standalone ||
                          document.referrer.includes('android-app://');

        console.log('App installed:', this.isInstalled);
        return this.isInstalled;
    }

    // ------------ NETWORK MONITORING METHODS

    /**
     * Setup network detection
     *
     * Configures event listeners for online/offline events to monitor
     * network connectivity changes and dispatch custom events.
     *
     * @method setupNetworkDetection
     * @returns {void}
     *
     * @example
     * pwaUtils.setupNetworkDetection();
     * // Automatically called during initialization
     */
    setupNetworkDetection() {
        window.addEventListener('online', () => {
            this.isOnline = true;
            this.onNetworkChange('online');
        });

        window.addEventListener('offline', () => {
            this.isOnline = false;
            this.onNetworkChange('offline');
        });
    }

    /**
     * Handle network changes
     *
     * Processes network status changes and dispatches custom events
     * for other parts of the application to respond to connectivity changes.
     *
     * @method onNetworkChange
     * @param {string} status - Network status: 'online' or 'offline'
     * @returns {void}
     *
     * @fires PWAUtils#pwa-network-change
     *
     * @example
     * // Listen for network changes
     * window.addEventListener('pwa-network-change', (event) => {
     *   const { status, isOnline } = event.detail;
     *   console.log(`Network is ${status}, online: ${isOnline}`);
     * });
     */
    onNetworkChange(status) {
        /** @type {CustomEvent} */
        const event = new CustomEvent('pwa-network-change', {
            detail: { status, isOnline: this.isOnline }
        });
        window.dispatchEvent(event);
    }

    // ------------ INSTALL PROMPT METHODS

    /**
     * Setup install prompt handling
     *
     * Configures event listeners for PWA installation events including
     * beforeinstallprompt and appinstalled events to manage the installation flow.
     *
     * @method setupInstallPrompt
     * @returns {void}
     *
     * @fires PWAUtils#pwa-install-available
     * @fires PWAUtils#pwa-installed
     *
     * @example
     * pwaUtils.setupInstallPrompt();
     * // Automatically called during initialization
     */
    setupInstallPrompt() {
        window.addEventListener('beforeinstallprompt', (e) => {
            e.preventDefault();
            /** @type {PWAInstallPrompt} */
            this.installPrompt = e;

            /** @type {CustomEvent} */
            const event = new CustomEvent('pwa-install-available', {
                detail: { prompt: e }
            });
            window.dispatchEvent(event);
        });

        window.addEventListener('appinstalled', () => {
            this.isInstalled = true;
            this.installPrompt = null;

            /** @type {CustomEvent} */
            const event = new CustomEvent('pwa-installed');
            window.dispatchEvent(event);
        });
    }

    // ------------ UPDATE DETECTION METHODS

    /**
     * Setup update detection
     *
     * Configures service worker event listeners to detect when app updates
     * are available and notify the application accordingly.
     *
     * @method setupUpdateDetection
     * @returns {void}
     *
     * @fires PWAUtils#pwa-update-available
     *
     * @example
     * pwaUtils.setupUpdateDetection();
     * // Automatically called during initialization
     */
    setupUpdateDetection() {
        if ('serviceWorker' in navigator) {
            navigator.serviceWorker.addEventListener('controllerchange', () => {
                this.updateAvailable = true;

                /** @type {CustomEvent} */
                const event = new CustomEvent('pwa-update-available');
                window.dispatchEvent(event);
            });
        }
    }

    // ------------ INSTALLATION MANAGEMENT METHODS

    /**
     * Trigger install prompt
     *
     * Shows the native browser install prompt to the user if available.
     * This method can only be called in response to a user gesture and
     * when an install prompt is available.
     *
     * @async
     * @method promptInstall
     * @returns {Promise<string>} User choice result: 'accepted' or 'dismissed'
     *
     * @throws {Error} When install prompt is not available
     *
     * @example
     * try {
     *   const result = await pwaUtils.promptInstall();
     *   console.log('Install prompt result:', result);
     * } catch (error) {
     *   console.error('Install prompt failed:', error);
     * }
     */
    async promptInstall() {
        if (!this.installPrompt) {
            throw new Error('Install prompt not available');
        }

        this.installPrompt.prompt();
        /** @type {{outcome: string}} */
        const { outcome } = await this.installPrompt.userChoice;

        if (outcome === 'accepted') {
            this.installPrompt = null;
        }

        return outcome;
    }

    /**
     * Check if app can be installed
     *
     * Determines whether the PWA installation prompt is available
     * and the app is not already installed.
     *
     * @method canInstall
     * @returns {boolean} True if app can be installed
     *
     * @example
     * if (pwaUtils.canInstall()) {
     *   // Show install button
     *   showInstallButton();
     * }
     */
    canInstall() {
        return !!this.installPrompt && !this.isInstalled;
    }

    // ------------ APP INFORMATION METHODS

    /**
     * Get comprehensive app information
     *
     * Returns detailed information about the current PWA state including
     * installation status, network connectivity, update availability,
     * and platform details.
     *
     * @method getAppInfo
     * @returns {PWAAppInfo} Comprehensive app information object
     *
     * @example
     * const appInfo = pwaUtils.getAppInfo();
     * console.log('App installed:', appInfo.isInstalled);
     * console.log('Network online:', appInfo.isOnline);
     * console.log('Can install:', appInfo.canInstall);
     */
    getAppInfo() {
        return {
            isInstalled: this.isInstalled,
            isOnline: this.isOnline,
            canInstall: this.canInstall(),
            updateAvailable: this.updateAvailable,
            standalone: window.matchMedia('(display-mode: standalone)').matches,
            userAgent: navigator.userAgent,
            platform: navigator.platform
        };
    }

    // ------------ WEB SHARE API METHODS

    /**
     * Share content using Web Share API
     *
     * Uses the native Web Share API to share content if supported by the browser.
     * Falls back gracefully if the API is not available or user cancels.
     *
     * @async
     * @method share
     * @param {PWAShareData} shareData - Data to share (title, text, url)
     * @returns {Promise<boolean>} True if sharing was successful, false if cancelled
     *
     * @throws {Error} When Web Share API is not supported or sharing fails
     *
     * @example
     * try {
     *   const success = await pwaUtils.share({
     *     title: 'The Great Calculator',
     *     text: 'Check out this amazing calculator!',
     *     url: window.location.href
     *   });
     *   console.log('Sharing successful:', success);
     * } catch (error) {
     *   console.error('Sharing failed:', error);
     * }
     */
    async share(shareData) {
        if (!navigator.share) {
            throw new Error('Web Share API not supported');
        }

        try {
            await navigator.share(shareData);
            return true;
        } catch (error) {
            if (error.name !== 'AbortError') {
                throw error;
            }
            return false;
        }
    }

    /**
     * Check if Web Share API is supported
     *
     * Determines whether the current browser supports the Web Share API
     * for native content sharing functionality.
     *
     * @method canShare
     * @returns {boolean} True if Web Share API is supported
     *
     * @example
     * if (pwaUtils.canShare()) {
     *   // Show share button
     *   showShareButton();
     * } else {
     *   // Use fallback sharing method
     *   showCopyLinkButton();
     * }
     */
    canShare() {
        return 'share' in navigator;
    }

    // ------------ STORAGE MANAGEMENT METHODS

    /**
     * Get storage usage information
     *
     * Retrieves detailed information about storage quota and usage
     * using the Storage API if available in the browser.
     *
     * @async
     * @method getStorageInfo
     * @returns {Promise<PWAStorageInfo|null>} Storage information or null if not supported
     *
     * @example
     * const storageInfo = await pwaUtils.getStorageInfo();
     * if (storageInfo) {
     *   console.log(`Using ${storageInfo.percentage}% of storage`);
     *   console.log(`${storageInfo.available} bytes available`);
     * }
     */
    async getStorageInfo() {
        if ('storage' in navigator && 'estimate' in navigator.storage) {
            /** @type {StorageEstimate} */
            const estimate = await navigator.storage.estimate();
            return {
                quota: estimate.quota || 0,
                usage: estimate.usage || 0,
                available: (estimate.quota || 0) - (estimate.usage || 0),
                percentage: Math.round(((estimate.usage || 0) / (estimate.quota || 1)) * 100)
            };
        }
        return null;
    }

    /**
     * Request persistent storage
     *
     * Requests persistent storage permission to prevent data from being
     * evicted by the browser under storage pressure.
     *
     * @async
     * @method requestPersistentStorage
     * @returns {Promise<boolean>} True if persistent storage was granted
     *
     * @example
     * const isPersistent = await pwaUtils.requestPersistentStorage();
     * if (isPersistent) {
     *   console.log('Storage will persist across browser sessions');
     * } else {
     *   console.log('Storage may be evicted under pressure');
     * }
     */
    async requestPersistentStorage() {
        if ('storage' in navigator && 'persist' in navigator.storage) {
            return await navigator.storage.persist();
        }
        return false;
    }

    /**
     * Check if storage is persistent
     *
     * Determines whether the current storage is marked as persistent
     * and protected from eviction by the browser.
     *
     * @async
     * @method isStoragePersistent
     * @returns {Promise<boolean>} True if storage is persistent
     *
     * @example
     * const isPersistent = await pwaUtils.isStoragePersistent();
     * console.log('Storage is persistent:', isPersistent);
     */
    async isStoragePersistent() {
        if ('storage' in navigator && 'persisted' in navigator.storage) {
            return await navigator.storage.persisted();
        }
        return false;
    }

    // ------------ PLATFORM-SPECIFIC METHODS

    /**
     * Get platform-specific install instructions
     *
     * Provides user-friendly installation instructions based on the
     * detected platform (iOS, Android, or Desktop) for manual installation.
     *
     * @method getInstallInstructions
     * @returns {PWAInstallInstructions} Platform-specific installation guidance
     *
     * @example
     * const instructions = pwaUtils.getInstallInstructions();
     * console.log(`${instructions.icon} ${instructions.platform}:`);
     * console.log(instructions.instructions);
     */
    getInstallInstructions() {
        /** @type {string} */
        const userAgent = navigator.userAgent.toLowerCase();

        if (userAgent.includes('iphone') || userAgent.includes('ipad')) {
            return {
                platform: 'iOS',
                instructions: 'Tap the Share button and select "Add to Home Screen"',
                icon: '📱'
            };
        } else if (userAgent.includes('android')) {
            return {
                platform: 'Android',
                instructions: 'Tap the menu button and select "Add to Home screen"',
                icon: '🤖'
            };
        } else {
            return {
                platform: 'Desktop',
                instructions: 'Look for the install button in your browser\'s address bar',
                icon: '💻'
            };
        }
    }

    // ------------ ANALYTICS AND LOGGING METHODS

    /**
     * Log PWA analytics event
     *
     * Records PWA-related events for analytics and debugging purposes.
     * Dispatches custom events that can be captured by analytics services.
     *
     * @method logEvent
     * @param {string} event - Event name identifier
     * @param {Object} [data={}] - Additional event data
     * @returns {void}
     *
     * @fires PWAUtils#pwa-analytics
     *
     * @example
     * // Log app installation
     * pwaUtils.logEvent('app_installed', {
     *   installMethod: 'browser_prompt'
     * });
     *
     * // Log network change
     * pwaUtils.logEvent('network_change', {
     *   status: 'offline',
     *   timestamp: Date.now()
     * });
     */
    logEvent(event, data = {}) {
        /** @type {PWAEventData} */
        const eventData = {
            event,
            timestamp: new Date().toISOString(),
            appInfo: this.getAppInfo(),
            ...data
        };

        console.log('PWA Event:', eventData);

        // Could be extended to send to analytics service
        /** @type {CustomEvent} */
        const customEvent = new CustomEvent('pwa-analytics', {
            detail: eventData
        });
        window.dispatchEvent(customEvent);
    }
}

// ------------ MODULE EXPORTS AND GLOBAL REGISTRATION

/**
 * Make PWAUtils available globally for legacy compatibility
 * and direct script inclusion scenarios.
 *
 * @global
 * @type {typeof PWAUtils}
 */
window.PWAUtils = PWAUtils;

/**
 * Export for CommonJS module systems (Node.js, older bundlers)
 * Provides compatibility with various module loading systems.
 *
 * @module PWAUtils
 * @type {typeof PWAUtils}
 */
if (typeof module !== 'undefined' && module.exports) {
    module.exports = PWAUtils;
}

/**
 * Export for ES6 module systems (modern bundlers, native ES modules)
 * Enables tree-shaking and modern import/export syntax.
 *
 * @default PWAUtils
 */
export default PWAUtils;
